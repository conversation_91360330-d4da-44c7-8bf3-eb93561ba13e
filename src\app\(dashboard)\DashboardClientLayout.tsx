"use client";

import { useEffect } from "react";
import Layout from "@/components/business/layout";
import { PerformanceMonitor } from "@/components/PerformanceMonitor";
import { Toaster } from "@/components/ui/sonner";
import { useAccountStore } from "@/store/account";
import useRoleStore from "@/store/persona";

interface DashboardClientLayoutProps {
  children: React.ReactNode;
}

export default function DashboardClientLayout({
  children,
}: DashboardClientLayoutProps) {
  const { fetchCurrentUser } = useAccountStore();
  const { getRoleList } = useRoleStore();

  useEffect(() => {
    fetchCurrentUser();
    getRoleList();
  }, [fetchCurrentUser, getRoleList]);

  return (
    <>
      <Toaster />
      <Layout>{children}</Layout>
      <PerformanceMonitor />
    </>
  );
}
