import { Loader } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { Select } from "@/components/business/select";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RequiredLabel } from "@/components/ui/RequiredLabel";
import { Textarea } from "@/components/ui/textarea";
import { useLanguage } from "@/hooks/useLanguage";
import { IPersona } from "@/services/api/persona";

const RoleForm = ({
  onConfirm,
  onclose,
  defaultValues,
  isUpdate,
}: {
  onConfirm: (data: IPersona) => void;
  onclose?: () => void;
  defaultValues?: Partial<IPersona>;
  isUpdate?: boolean;
}) => {
  const { t } = useLanguage();
  const [isFetching, setIsFetching] = useState(false);

  // 开发环境下显示调试信息
  if (process.env.NODE_ENV === "development") {
    console.log("RoleForm props:", { defaultValues, isUpdate });
  }

  // 初始化表单
  const form = useForm<IPersona>({
    defaultValues: {
      name: defaultValues?.name || "",
      gender: defaultValues?.gender || "",
      mbti: defaultValues?.mbti || "",
      personality: defaultValues?.personality || "",
      introduction: defaultValues?.introduction || "",
      topics: defaultValues?.topics || "",
    },
    mode: "onChange", // 实时验证
  });

  useEffect(() => {
    // 当 defaultValues 发生变化时，重新设置表单值
    if (defaultValues && Object.keys(defaultValues).length > 0) {
      const formData = {
        name: defaultValues.name || "",
        gender: defaultValues.gender || "",
        mbti: defaultValues.mbti || "",
        personality: defaultValues.personality || "",
        introduction: defaultValues.introduction || "",
        topics: defaultValues.topics || "",
      };

      // 开发环境下显示调试信息
      if (process.env.NODE_ENV === "development") {
        console.log("RoleForm defaultValues:", defaultValues);
        console.log("RoleForm formData:", formData);
        console.log("Current form values:", form.getValues());
      }

      form.reset(formData);
    }
  }, [defaultValues, form]);

  const onCancel = () => {
    form.reset();
    onclose?.();
  };
  const onSubmit = async (data: IPersona) => {
    setIsFetching(true);
    await onConfirm?.(data);
    setIsFetching(false);
  };
  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="text-text-secondary flex flex-col gap-6"
      >
        {/* 人设名称 */}
        <FormField
          control={form.control}
          name="name"
          rules={{
            required: t("modal:addRole.validation.nameRequired"),
            minLength: {
              value: 2,
              message: t("modal:addRole.validation.nameMinLength"),
            },
            maxLength: {
              value: 20,
              message: t("modal:addRole.validation.nameMaxLength"),
            },
          }}
          render={({ field }) => (
            <FormItem>
              <RequiredLabel required>
                {t("modal:addRole.roleName")}
              </RequiredLabel>
              <FormControl>
                <Input
                  placeholder={t("modal:addRole.roleNamePlaceholder")}
                  className="h-12"
                  maxLength={20}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 性别和MBTI类型 */}
        <div className="grid grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="gender"
            rules={{
              required: t("modal:addRole.validation.genderRequired"),
            }}
            render={({ field }) => {
              // 开发环境下显示调试信息
              if (process.env.NODE_ENV === "development") {
                console.log("Gender field value:", field.value);
              }

              return (
                <FormItem>
                  <RequiredLabel required>
                    {t("modal:addRole.gender")}
                  </RequiredLabel>
                  <FormControl>
                    <Select
                      placeholder={t("modal:addRole.genderPlaceholder")}
                      className="w-full"
                      options={[
                        { name: t("modal:addRole.male"), value: "male" },
                        { name: t("modal:addRole.female"), value: "female" },
                      ]}
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />

          <FormField
            control={form.control}
            name="mbti"
            rules={{
              required: t("modal:addRole.validation.mbtiRequired"),
            }}
            render={({ field }) => (
              <FormItem>
                <RequiredLabel required>
                  {t("modal:addRole.mbtiType")}
                </RequiredLabel>
                <FormControl>
                  <Select
                    placeholder={t("modal:addRole.mbtiPlaceholder")}
                    className="w-full"
                    options={[
                      {
                        name: t("modal:addRole.mbtiOptions.ENFP"),
                        value: "ENFP",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.INFP"),
                        value: "INFP",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ENFJ"),
                        value: "ENFJ",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.INFJ"),
                        value: "INFJ",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ENTP"),
                        value: "ENTP",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.INTP"),
                        value: "INTP",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ENTJ"),
                        value: "ENTJ",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.INTJ"),
                        value: "INTJ",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ESFP"),
                        value: "ESFP",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ISFP"),
                        value: "ISFP",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ESFJ"),
                        value: "ESFJ",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ISFJ"),
                        value: "ISFJ",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ESTP"),
                        value: "ESTP",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ISTP"),
                        value: "ISTP",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ESTJ"),
                        value: "ESTJ",
                      },
                      {
                        name: t("modal:addRole.mbtiOptions.ISTJ"),
                        value: "ISTJ",
                      },
                    ]}
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* 性格特点 */}
        <FormField
          control={form.control}
          name="personality"
          render={({ field }) => (
            <FormItem>
              <RequiredLabel>{t("modal:addRole.personality")}</RequiredLabel>
              <FormControl>
                <Input
                  placeholder={t("modal:addRole.personalityPlaceholder")}
                  className="h-12"
                  maxLength={2000}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 详细介绍 */}
        <FormField
          control={form.control}
          name="introduction"
          rules={{
            required: t("modal:addRole.validation.introductionRequired"),
            minLength: {
              value: 10,
              message: t("modal:addRole.validation.introductionMinLength"),
            },
          }}
          render={({ field }) => (
            <FormItem>
              <RequiredLabel required>
                {t("modal:addRole.roleIntro")}
              </RequiredLabel>
              <FormControl>
                <Textarea
                  placeholder={t("modal:addRole.roleIntroPlaceholder")}
                  className="min-h-20"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 擅长话题 */}
        <FormField
          control={form.control}
          name="topics"
          render={({ field }) => (
            <FormItem>
              <RequiredLabel>
                {t("modal:addRole.topicPreference")}
              </RequiredLabel>
              <FormControl>
                <Input
                  placeholder={t("modal:addRole.topicPlaceholder")}
                  className="h-12"
                  maxLength={2000}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 操作按钮 */}
        <div className="grid gap-10 grid-cols-2">
          <Button type="button" variant="secondary" onClick={onCancel}>
            {t("button:cancel")}
          </Button>
          <Button
            type="submit"
            disabled={isFetching}
            className="text-text-primary"
          >
            {isFetching && <Loader className="mr-1 h-4 w-4 spin360" />}
            {isUpdate ? t("button:update") : t("button:create")}
          </Button>
        </div>
      </form>
    </Form>
  );
};
export default RoleForm;
