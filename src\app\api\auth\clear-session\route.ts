import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    console.log("Clear session API called");

    const response = NextResponse.json({
      success: true,
      message: "Session cleared",
    });

    // 清除所有可能的NextAuth cookies
    const cookieNames = [
      "next-auth.session-token",
      "next-auth.csrf-token",
      "next-auth.callback-url",
      "next-auth.state",
      "__Secure-next-auth.session-token",
      "__Host-next-auth.csrf-token",
      "nextauth.session-token",
      "nextauth.csrf-token",
    ];

    // 清除所有相关cookies
    cookieNames.forEach((name) => {
      response.cookies.set(name, "", {
        expires: new Date(0),
        path: "/",
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 0,
      });
    });

    console.log("Session cookies cleared");
    return response;
  } catch (error) {
    console.error("Clear session error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to clear session" },
      { status: 500 }
    );
  }
}
