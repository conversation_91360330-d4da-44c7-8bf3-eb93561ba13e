import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

// 需要保护的路由
const protectedRoutes = [
  "/",
  "/home",
  "/work-space",
  "/camera-roll",
  "/information",
  "/setting-sys",
];

// 公开路由（不需要认证）
const publicRoutes = ["/login", "/api/auth", "/debug-auth"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 暂时禁用middleware进行调试
  console.log("Middleware - 暂时禁用，允许所有访问");
  return NextResponse.next();

  // 跳过静态文件和API路由（除了需要保护的API）
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/static") ||
    pathname.includes(".") ||
    publicRoutes.some((route) => pathname.startsWith(route))
  ) {
    return NextResponse.next();
  }

  // 检查是否是受保护的路由
  const isProtectedRoute = protectedRoutes.some(
    (route) => pathname === route || pathname.startsWith(route + "/")
  );

  if (!isProtectedRoute) {
    return NextResponse.next();
  }

  try {
    // 检查NextAuth session
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    console.log("Middleware - Path:", pathname);
    console.log("Middleware - Token:", token ? "存在" : "不存在");
    console.log(
      "Middleware - SmartiesToken:",
      token?.smartiesToken ? "存在" : "不存在"
    );

    // 如果有有效的NextAuth session且有smartiesToken，允许访问
    if (token?.smartiesToken) {
      console.log("Middleware - 允许访问");
      return NextResponse.next();
    }

    // 如果没有有效session，重定向到登录页
    console.log("Middleware - 重定向到登录页");
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("callbackUrl", pathname);
    return NextResponse.redirect(loginUrl);
  } catch (error) {
    console.error("Middleware error:", error);
    // 发生错误时重定向到登录页
    const loginUrl = new URL("/login", request.url);
    return NextResponse.redirect(loginUrl);
  }
}

export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api routes (除了需要保护的)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (public folder)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)",
  ],
};
