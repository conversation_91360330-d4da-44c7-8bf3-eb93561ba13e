import { api } from "@/lib/api";
import type { ApiResponse } from "@/types";

export interface IPersona {
  userId: string; // 用户ID
  personaId: string; // 人设唯一标识
  name: string; // 人设名称
  gender: string; // 性别
  mbti: string; // MBTI类型
  personality?: string; // 性格特点
  introduction?: string; // 详细介绍
  topics?: string; // 擅长话题
  isDefault?: boolean; // 是否默认人设
  createdAt?: string;
  updatedAt?: string;
}
export const personaCreateApi = (
  data: IPersona
): Promise<
  ApiResponse<{
    writingStyle: string;
    contentLength: string;
    writingFields: string[];
  }>
> => {
  return api.post(`/persona/create`, data);
};

// list
export const personaListApi = (): Promise<
  ApiResponse<{
    count: number;
    personas: IPersona[];
  }>
> => {
  return api.post(`/persona/list`);
};

// set-default
export const personaSetDefaultApi = (
  personaId: string
): Promise<
  ApiResponse<{
    success: boolean;
  }>
> => {
  return api.post(`/persona/set-default`, { personaId });
};
