import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import DashboardClientLayout from "./DashboardClientLayout";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // 在服务端进行认证检查
  const session = await getServerSession(authOptions);

  console.log("Dashboard layout - Session:", session);
  console.log(
    "Dashboard layout - SmartiesToken:",
    (session as any)?.smartiesToken
  );

  // 如果没有session或没有smartiesToken，重定向到登录页
  if (!session || !(session as any).smartiesToken) {
    console.log("Dashboard layout - No valid session, redirecting to login");
    redirect("/login");
  }

  console.log("Dashboard layout - Valid session, rendering dashboard");
  // 认证通过，渲染客户端布局
  return <DashboardClientLayout>{children}</DashboardClientLayout>;
}
