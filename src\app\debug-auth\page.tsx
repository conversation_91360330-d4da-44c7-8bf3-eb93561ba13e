"use client";

import { signOut, useSession } from "next-auth/react";
import { useEffect, useState } from "react";

export default function DebugAuthPage() {
  const { data: session, status } = useSession();
  const [tokenInfo, setTokenInfo] = useState<any>(null);
  const [isClearing, setIsClearing] = useState(false);

  useEffect(() => {
    const checkToken = async () => {
      try {
        const response = await fetch("/api/auth/session");
        const sessionData = await response.json();
        setTokenInfo(sessionData);
      } catch (error) {
        console.error("Error fetching session:", error);
      }
    };

    checkToken();
  }, []);

  const handleClearSession = async () => {
    setIsClearing(true);
    try {
      console.log("Clearing session...");

      // 调用清除session API
      const response = await fetch("/api/auth/clear-session", {
        method: "POST",
      });
      const result = await response.json();
      console.log("Clear session result:", result);

      // 调用NextAuth signOut
      await signOut({ redirect: false });
      console.log("NextAuth signOut completed");

      // 清除localStorage
      if (typeof window !== "undefined") {
        localStorage.clear();
        console.log("localStorage cleared");
      }

      // 强制刷新页面
      window.location.href = "/login";
    } catch (error) {
      console.error("Failed to clear session:", error);
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">认证调试页面</h1>

      <div className="space-y-6">
        <div className="bg-red-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">清除Session</h2>
          <p className="text-sm text-gray-600 mb-4">
            如果遇到重定向循环问题，点击下面的按钮清除所有session数据
          </p>
          <button
            onClick={handleClearSession}
            disabled={isClearing}
            className="bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white px-4 py-2 rounded"
          >
            {isClearing ? "清除中..." : "清除Session"}
          </button>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Session Status</h2>
          <p>Status: {status}</p>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">
            Session Data (useSession)
          </h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(session, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Session Data (API)</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(tokenInfo, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Environment Variables</h2>
          <div className="text-sm">
            <p>NEXTAUTH_URL: {process.env.NEXTAUTH_URL || "未设置"}</p>
            <p>NODE_ENV: {process.env.NODE_ENV}</p>
          </div>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Local Storage</h2>
          <pre className="text-sm overflow-auto">
            {typeof window !== "undefined"
              ? JSON.stringify(
                  {
                    nextauth: localStorage.getItem("nextauth.session-token"),
                    theme: localStorage.getItem("theme"),
                    i18nextLng: localStorage.getItem("i18nextLng"),
                  },
                  null,
                  2
                )
              : "服务端渲染"}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Cookies</h2>
          <pre className="text-sm overflow-auto">
            {typeof window !== "undefined" ? document.cookie : "服务端渲染"}
          </pre>
        </div>
      </div>
    </div>
  );
}
