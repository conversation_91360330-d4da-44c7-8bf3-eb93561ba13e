"use client";

import { GalleryVerticalEnd } from "lucide-react";
import { useRouter } from "next/navigation";
import { signIn, signOut, useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLanguage } from "@/hooks/useLanguage";
import { cn } from "@/lib/utils";
import { IUser } from "@/services/api/user";
import { useAccountStore } from "@/store/account";

function LoginForm({ className, ...props }: React.ComponentProps<"div">) {
  const { t, currentLanguage } = useLanguage();
  const router = useRouter();
  const { data: session, status } = useSession();
  const { loginForm, setLoginForm, syncSession } = useAccountStore();
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  // 监听NextAuth session变化
  // useEffect(() => {
  //   console.log("Login page - Session status:", status);
  //   console.log("Login page - Session:", session);
  //   console.log("Login page - SmartiesToken:", session?.smartiesToken);

  //   if (
  //     status === "authenticated" &&
  //     session?.smartiesToken &&
  //     session?.userId
  //   ) {
  //     console.log("Login page - Valid session found, redirecting to home");
  //     // 将NextAuth用户数据转换为IUser格式
  //     const user: IUser = {
  //       id: session.userId,
  //       username: session.user?.name || "",
  //       email: session.user?.email || "",
  //       avatar: session.user?.image || "",
  //       phone: (session as any).user?.phone || "", // 手机号登录时会有手机号
  //       provider: (session as any).provider || "unknown",
  //     };

  //     // 同步session到本地状态
  //     syncSession({
  //       token: session.smartiesToken,
  //       userId: session.userId,
  //       user,
  //     });
  //     router.push("/");
  //   } else if (status === "authenticated" && !session?.smartiesToken) {
  //     console.log(
  //       "Login page - Invalid session (no smartiesToken), clearing session"
  //     );
  //     // 清除无效的session
  //     const clearSession = async () => {
  //       try {
  //         await fetch("/api/auth/clear-session", { method: "POST" });
  //         await signOut({ redirect: false });
  //         // 强制刷新页面
  //         window.location.reload();
  //       } catch (error) {
  //         console.error("Failed to clear session:", error);
  //       }
  //     };
  //     clearSession();
  //   }
  // }, [session, status, syncSession, router]);

  const handleLogin = async () => {
    setIsLoading(true);
    try {
      // 使用NextAuth的signIn方法进行手机号登录
      const result = await signIn("phone", {
        phone: loginForm.phone,
        code: loginForm.code,
        redirect: false, // 不自动重定向，手动处理
      });

      if (result?.ok) {
        // 登录成功，跳转到首页
        router.push("/");
      } else {
        console.error("Login failed:", result?.error);
      }
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true);
    try {
      await signIn("google", {
        callbackUrl: "/",
        redirect: true,
      });
    } catch (error) {
      console.error("Google login error:", error);
    } finally {
      setIsGoogleLoading(false);
    }
  };
  const isZh = currentLanguage === "zh-CN";
  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <form>
        <div className="flex flex-col gap-6">
          <div className="flex flex-col items-center gap-2">
            <a className="flex flex-col items-center gap-2 font-medium">
              <div className="flex size-8 items-center justify-center rounded-md">
                <GalleryVerticalEnd className="size-6" />
              </div>
            </a>
            <h1 className="text-xl font-bold">{t("login:login.title")}</h1>
          </div>
          {isZh ? (
            <div className="flex flex-col gap-6">
              <div className="grid gap-3">
                <Label htmlFor="phone">{t("login:login.phone")}</Label>
                <Input
                  id="phone"
                  placeholder="13800138000"
                  value={loginForm.phone}
                  onChange={(e) => setLoginForm({ phone: e.target.value })}
                  required
                />
              </div>
              <div className="grid gap-3">
                <Label htmlFor="code">{t("login:login.code")}</Label>
                <Input
                  id="email"
                  placeholder="123456"
                  value={loginForm.code}
                  onChange={(e) => setLoginForm({ code: e.target.value })}
                  required
                />
              </div>
              <Button
                className="w-full"
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  handleLogin();
                }}
                disabled={isLoading}
              >
                {isLoading ? t("button:loading") : t("button:login")}
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 grid-cols-1">
              <Button
                variant="secondary"
                type="button"
                className="w-full"
                onClick={handleGoogleLogin}
                disabled={isGoogleLoading || status === "loading"}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  className="w-4 h-4 mr-2"
                >
                  <path
                    d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                    fill="currentColor"
                  />
                </svg>
                {isGoogleLoading
                  ? t("button:loading")
                  : t("button:googleLogin")}
              </Button>
            </div>
          )}
          {/* <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
            <span className="bg-background text-muted-foreground relative z-10 px-2">
              {t("login:or")}
            </span>
          </div> */}
        </div>
      </form>
      {/* <div className="text-muted-foreground *:[a]:hover:text-primary text-center text-xs text-balance *:[a]:underline *:[a]:underline-offset-4">
        By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
        and <a href="#">Privacy Policy</a>.
      </div> */}
    </div>
  );
}

export default LoginForm;
