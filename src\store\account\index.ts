import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import {
  IUser,
  getCurrentUserApi,
  // syncSessionApi,
} from "@/services/api/user";
interface AccountStore {
  loginForm: {
    phone?: string;
    code?: string;
  };
  user: IUser;
  setLoginForm: (form: AccountStore["loginForm"]) => void;
  syncSession: (sessionData: {
    token: string;
    userId: string;
    user: IUser;
  }) => Promise<void>;
  fetchCurrentUser: () => Promise<void>;
  logout: () => void;
}
export const useAccountStore = create<AccountStore>()(
  immer((set, get) => ({
    loginForm: {
      phone: "***********",
      code: "123456",
    },
    user: {} as IUser,

    setLoginForm: (form: AccountStore["loginForm"]) => {
      set({
        loginForm: {
          ...get().loginForm,
          ...form,
        },
      });
    },

    // 同步NextAuth session
    async syncSession(sessionData: {
      token: string;
      userId: string;
      user: IUser;
    }) {
      try {
        // 只更新本地用户状态，token由NextAuth管理
        set({ user: sessionData.user });
      } catch (error) {
        console.error("Failed to sync session:", error);
      }
    },

    // 获取当前用户
    async fetchCurrentUser() {
      try {
        const res = await getCurrentUserApi();
        if (res.success && res.data) {
          set({ user: res.data.user });
        }
      } catch (error) {
        console.error("Failed to fetch current user:", error);
      }
    },

    // 登出
    logout() {
      set({
        user: {} as IUser,
        loginForm: { phone: "", code: "" },
      });
      // token由NextAuth管理，这里只清理本地状态
    },
  }))
);
