"use client";

import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { validateTokenApi } from "@/services/api/user";

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

/**
 * 客户端认证守卫组件
 * 用于在客户端进行额外的token验证（作为服务端验证的补充）
 */
export default function AuthGuard({
  children,
  fallback = <div>Loading...</div>,
  redirectTo = "/login",
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isValidating, setIsValidating] = useState(true);
  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    const validateAuth = async () => {
      try {
        // 如果没有session，直接重定向
        if (status === "unauthenticated") {
          router.push(redirectTo);
          return;
        }

        // 如果session还在加载中，等待
        if (status === "loading") {
          return;
        }

        // 如果有session但没有smartiesToken，可能需要重新登录
        if (session && !(session as any).smartiesToken) {
          console.warn("Session exists but no smartiesToken found");
          router.push(redirectTo);
          return;
        }

        // 验证token
        if ((session as any).smartiesToken) {
          const result = await validateTokenApi();
          if (result.success) {
            setIsValid(true);
          } else {
            // Token无效，清除本地存储并重定向
            if (typeof window !== "undefined") {
              localStorage.clear();
            }
            router.push(redirectTo);
          }
        }
      } catch (error) {
        console.error("Auth validation error:", error);
        // 发生错误时重定向到登录页
        router.push(redirectTo);
      } finally {
        setIsValidating(false);
      }
    };

    validateAuth();
  }, [session, status, router, redirectTo]);

  // 如果正在验证或session正在加载，显示fallback
  if (status === "loading" || isValidating) {
    return <>{fallback}</>;
  }

  // 如果验证失败，不渲染任何内容（因为会重定向）
  if (!isValid) {
    return null;
  }

  // 验证通过，渲染子组件
  return <>{children}</>;
}
