"use client";

import { Card } from "@/components/business/card";
import RoleForm from "@/components/business/modal/RoleForm";
import { IPersona } from "@/services/api/persona";
import useRoleStore from "@/store/persona";

const UserSettings = () => {
  const { createRole, currentRole } = useRoleStore();
  const handleCreate = (data: IPersona) => {
    createRole(data);
  };
  console.log("currentRole", currentRole);

  return (
    <Card>
      <RoleForm
        key={currentRole?.personaId || "empty"} // 使用 key 强制重新渲染
        defaultValues={currentRole}
        isUpdate
        onConfirm={(data) => handleCreate(data)}
      />
    </Card>
  );
};

export default UserSettings;
