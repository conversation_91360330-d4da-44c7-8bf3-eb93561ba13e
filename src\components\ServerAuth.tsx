import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { ReactNode } from "react";

interface ServerAuthProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
}

/**
 * 服务端认证组件
 * 在服务端验证用户认证状态，如果未认证则重定向到登录页
 */
export default async function ServerAuth({
  children,
  fallback = null,
  redirectTo = "/login",
}: ServerAuthProps) {
  try {
    // 获取NextAuth session
    const session = await getServerSession();

    // 如果没有session或没有smartiesToken，重定向到登录页
    if (!session || !(session as any).smartiesToken) {
      redirect(redirectTo);
    }

    // 认证通过，渲染子组件
    return <>{children}</>;
  } catch (error) {
    console.error("ServerAuth error:", error);

    // 如果提供了fallback，渲染它
    if (fallback) {
      return <>{fallback}</>;
    }

    // 否则重定向到登录页
    redirect(redirectTo);
  }
}
