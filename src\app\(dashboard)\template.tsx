"use client";

import { motion } from "framer-motion";
import { RouteErrorBoundary } from "@/components/RouteErrorBoundary";

export default function Template({ children }: { children: React.ReactNode }) {
  // 移除客户端token校验，因为已经在服务端layout中进行了校验
  return (
    <RouteErrorBoundary>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.15, ease: "easeOut" }}
        className="h-full"
      >
        {children}
      </motion.div>
    </RouteErrorBoundary>
  );
}
