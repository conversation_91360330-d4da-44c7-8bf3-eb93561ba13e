import { AuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { createLambdaClient } from "@/lib/lambda";

const lambdaClient = createLambdaClient();

// 验证必需的环境变量
if (process.env.NODE_ENV === "production") {
  if (!process.env.NEXTAUTH_SECRET) {
    throw new Error(
      "NEXTAUTH_SECRET must be set in production. " +
        "Generate a secret with: openssl rand -base64 32"
    );
  }
  if (!process.env.NEXTAUTH_URL) {
    throw new Error("NEXTAUTH_URL must be set in production");
  }
}

// NextAuth配置
export const authOptions: AuthOptions = {
  providers: [
    // 手机号登录
    CredentialsProvider({
      id: "phone",
      name: "Phone",
      credentials: {
        phone: { label: "Phone", type: "text" },
        code: { label: "Code", type: "text" },
      },
      async authorize(credentials) {
        console.log("Phone login - Credentials:", credentials);

        if (!credentials?.phone || !credentials?.code) {
          console.log("Phone login - Missing credentials");
          return null;
        }

        try {
          // 调用Lambda函数进行手机号登录
          const response = await lambdaClient.invoke({
            FunctionName: "smarties-auth-dev-phoneLogin",
            Payload: JSON.stringify({
              phone: credentials.phone,
              code: credentials.code,
            }),
          });

          console.log("Phone login - Lambda response:", response);

          if (!response.Payload) {
            console.log("Phone login - No payload in response");
            return null;
          }

          const result = JSON.parse(response.Payload.toString());
          console.log("Phone login - Parsed result:", result);

          if (result.success && result.data) {
            const userData = {
              id: result.data.userId,
              name: credentials.phone,
              email: "",
              image: "",
              phone: credentials.phone,
              provider: "phone",
              smartiesToken: result.data.token,
              userData: result.data.userData,
            };
            console.log("Phone login - Success, returning user data");
            return userData;
          } else {
            console.log("Phone login - Failed:", result.error || "Unknown error");
            return null;
          }
        } catch (error) {
          console.error("Phone login - Error:", error);
          return null;
        }
      },
    }),
  ],
  pages: {
    signIn: "/login",
    error: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  callbacks: {
    async jwt({ token, account, profile, user }: any): Promise<any> {
      console.error("JWT callback called with:", {
        account: account?.provider,
        hasUser: !!user,
        tokenExists: !!token.smartiesToken,
      });

      // 处理手机号登录
      if (account?.provider === "phone" && user) {
        console.error("JWT callback - Phone login user:", user);
        token.smartiesToken = user.smartiesToken;
        token.userId = user.id;
        token.user = user.userData || {
          username: user.name,
          email: user.email,
          avatar: user.image,
          phone: user.phone,
        };
        token.provider = "phone";
        console.error("JWT callback - Phone login token:", token);
        return token;
      }

      // 如果没有account但有现有token，直接返回现有token
      if (!account && token.smartiesToken) {
        console.error(
          "JWT callback - Returning existing token with smartiesToken"
        );
        return token;
      }

      // 如果没有account也没有smartiesToken，说明是无效session
      if (!account) {
        console.error(
          "JWT callback - No account and no smartiesToken, returning token as-is"
        );
        return token;
      }

      // 处理其他OAuth登录（如果有的话）
      if (account && user) {
        token.userId = user.id;
        token.user = user;
        token.provider = account.provider;
        return token;
      }

      return token;
    },

    async session({ session, token }: { session: any; token: any }) {
      console.error("Session callback called with token:", {
        hasToken: !!token,
        hasSmartiesToken: !!token?.smartiesToken,
        tokenKeys: token ? Object.keys(token) : [],
      });

      if (token && token.smartiesToken) {
        console.error("Session callback - Setting smartiesToken");
        const updatedSession = {
          ...session,
          smartiesToken: token.smartiesToken,
          userId: token.userId,
          user: {
            ...session.user,
            id: token.userId,
            ...token.user,
          },
        };
        console.error("Session callback - Final session:", {
          hasSmartiesToken: !!updatedSession.smartiesToken,
          userId: updatedSession.userId,
          smartiesTokenLength: updatedSession.smartiesToken?.length,
        });
        console.error(
          "Session callback - Returning session with keys:",
          Object.keys(updatedSession)
        );
        console.error(
          "Session callback - Complete session object:",
          JSON.stringify(updatedSession, null, 2)
        );
        return updatedSession;
      }

      console.error(
        "Session callback - No valid token, returning session without smartiesToken"
      );
      // 返回基本session，不要返回null
      return session;
    },
  },
  debug: process.env.NODE_ENV === "development",
  secret: process.env.NEXTAUTH_SECRET,
};
