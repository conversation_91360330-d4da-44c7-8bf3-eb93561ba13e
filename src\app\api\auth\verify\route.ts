import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";

export async function POST(request: NextRequest) {
  try {
    // 获取请求中的token
    const authHeader = request.headers.get("authorization");
    const token = authHeader?.replace("Bearer ", "");

    if (!token) {
      return NextResponse.json(
        { success: false, error: "No token provided" },
        { status: 401 }
      );
    }

    // 检查NextAuth session
    const session = await getServerSession();

    // 如果有session且token匹配，验证通过
    if (session && (session as any).smartiesToken === token) {
      return NextResponse.json({
        success: true,
        message: "Token is valid",
        user: session.user,
      });
    }

    // 如果没有session或token不匹配，调用后端API验证
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/auth/verify-token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    const result = await response.json();

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: "Token is valid",
        user: result.user,
      });
    } else {
      return NextResponse.json(
        { success: false, error: "Invalid token" },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error("Token verification error:", error);
    return NextResponse.json(
      { success: false, error: "Token verification failed" },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // 获取当前session状态
    const session = await getServerSession();

    return NextResponse.json({
      success: true,
      hasSession: !!session,
      session: session
        ? {
            user: session.user,
            expires: session.expires,
            smartiesToken: (session as any).smartiesToken ? "***" : null,
          }
        : null,
    });
  } catch (error) {
    console.error("Session check error:", error);
    return NextResponse.json(
      { success: false, error: "Session check failed" },
      { status: 500 }
    );
  }
}
